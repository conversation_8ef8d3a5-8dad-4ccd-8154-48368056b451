import logging
import os
import sys
import threading
from telegram import Update
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
    MessageHandler,
    filters,
    JobQueue
)
from utils import send_db_backup
import pytz
from datetime import datetime, time

from config import TELEGRAM_TOKEN, NOTIFICATION_INTERVAL
from database import init_db
from payment_utils import app as flask_app
from handlers import (
    start, gift_command, handle_message,
    ref_command, add_balance_command, add_gift_command, check_gift_command,
    announce, userinfo_command, stats_command, reset_balance_command, sync,
    button_handler, add_days_command, test_api_command
)
from handlers.admin_handlers import backupdb_command, sync_remnawave_command
from notification_service import send_expiration_warnings

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Используем константу из конфигурации
NOTIFICATION_CHECK_INTERVAL = NOTIFICATION_INTERVAL  # Интервал из config.py

def run_flask():
    flask_app.run(host='0.0.0.0', port=5000, debug=False)

def main():
    # Initialize database
    init_db()
    
    # Start Flask server in a separate thread
    flask_thread = threading.Thread(target=run_flask)
    flask_thread.daemon = True
    flask_thread.start()
    
    # Initialize the bot
    application = Application.builder().token(TELEGRAM_TOKEN).build()
    
    # Add handlers for commands
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("gift", gift_command))
    application.add_handler(CommandHandler("ref", ref_command))
    application.add_handler(CommandHandler("add_balance", add_balance_command))
    application.add_handler(CommandHandler("addgift", add_gift_command))
    application.add_handler(CommandHandler("checkgift", check_gift_command))
    application.add_handler(CommandHandler("announce", announce))
    application.add_handler(CommandHandler("userinfo", userinfo_command))
    application.add_handler(CommandHandler("stats", stats_command))
    application.add_handler(CommandHandler("reset_balance", reset_balance_command))
    application.add_handler(CommandHandler("sync", sync))
    application.add_handler(CommandHandler("add_days", add_days_command))
    application.add_handler(CommandHandler("test_api", test_api_command))
    application.add_handler(CommandHandler("backupdb", backupdb_command))
    application.add_handler(CommandHandler("sync_remnawave", sync_remnawave_command))
    
    # Add handler for callback queries (button clicks)
    application.add_handler(CallbackQueryHandler(button_handler))
    
    # Add handler for messages
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    
    # Add job for subscription expiry check (запускаем каждые 15 минут с небольшой начальной задержкой)
    job_queue = application.job_queue
    job_queue.run_repeating(lambda context: send_expiration_warnings(application.bot), 
                           interval=NOTIFICATION_CHECK_INTERVAL, 
                           first=60)  # Первый запуск через 60 секунд после старта

    # Add job for daily DB backup at 12:00 MSK
    msk_tz = pytz.timezone('Europe/Moscow')
    job_queue.run_daily(send_db_backup, time=time(hour=12, minute=0, tzinfo=msk_tz), days=(0,1,2,3,4,5,6), data={"bot": application.bot})
    
    # Log that notification system is initialized
    logger.info(f"Система уведомлений инициализирована. Интервал проверки: {NOTIFICATION_CHECK_INTERVAL} секунд")
    
    # Start the Bot
    application.run_polling()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())