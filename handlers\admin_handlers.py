import logging
import asyncio
import uuid
import pytz
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.error import BadRequest
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from database import get_db_connection, add_balance, add_subscription_history
from config import ADMIN_IDS
from utils import escape_markdown_v2, format_date, send_db_backup, send_db_backup_manual
from remnawave_utils import user_exists, sync_user_with_remnawave, extend_remnawave_subscription, test_remnawave_connection, test_inbound_configuration, sync_all_remnawave_users_with_database
from remnawave_api.enums.users import UserStatus
from handlers.states import user_states, payment_messages

def get_subscription_status_admin(user_info, expire_date, current_time):
    """
    Определяет статус подписки на основе данных из Remnawave для админ-панели

    Args:
        user_info: Объект пользователя из Remnawave API
        expire_date: Дата истечения подписки
        current_time: Текущее время

    Returns:
        str: Статус подписки
    """
    if not user_info:
        return "❌ Неактивна"

    # Проверяем статус пользователя в Remnawave
    user_status = getattr(user_info, 'status', None)

    if user_status == UserStatus.DISABLED:
        return "⏸️ Отключена"
    elif user_status == UserStatus.LIMITED:
        return "⚠️ Ограничена"
    elif user_status == UserStatus.EXPIRED:
        return "❌ Истекла"
    elif user_status == UserStatus.ACTIVE:
        # Для активного статуса дополнительно проверяем дату истечения
        if expire_date and expire_date > current_time:
            return "✅ Активна"
        else:
            return "❌ Истекла"
    else:
        # Если статус неизвестен, используем старую логику по дате
        if expire_date and expire_date > current_time:
            return "✅ Активна"
        else:
            return "❌ Неактивна"

async def ref_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    if not context.args:
        await update.message.reply_text("❌ Укажите ID: /ref <ID>")
        return
    try:
        target_user_id = int(context.args[0])
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT user_id, username, trial_active, subscription_active, trial_end_date, subscription_end_date FROM users WHERE referred_by = ?', (target_user_id,))
            referrals = c.fetchall()
            if not referrals:
                await update.message.reply_text("👥 Нет рефералов.")
                return
            info = "👥 *Рефералы:*\n\n"
            for ref in referrals:
                ref_id, username, trial_active, sub_active, trial_end, sub_end = ref
                status = "❌ Не активирована"
                end_date = await format_date(sub_end if sub_active else trial_end)
                if sub_active:
                    status = "✅ Активна (платная)"
                elif trial_active:
                    status = "✅ Активна (пробная)"
                info += f"🆔 `{ref_id}`\n👤 `{username or 'Неизвестно'}`\n📊 {status}\n📅 {end_date}\n\n"
            await update.message.reply_text(info, parse_mode='Markdown')
    except Exception as e:
        await update.message.reply_text(f"❌ Ошибка: {e}")

async def add_balance_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    try:
        user_id, amount = int(context.args[0]), float(context.args[1])
        if add_balance(user_id, amount):
            await update.message.reply_text(f"✅ Баланс {user_id} пополнен на {amount}₽")
            await context.bot.send_message(user_id, f"💰 Ваш баланс пополнен на {amount}₽", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]]))
    except (IndexError, ValueError):
        await update.message.reply_text("❌ Формат: /add_balance <user_id> <amount>")

async def add_gift_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return

    if len(context.args) < 3:
        await update.message.reply_text(
            "❌ Формат: /addgift <название_кода> <сумма> <число пользователей>\n"
            "Например (одно слово): /addgift SUPERCODE 500 10\n"
            "Например (несколько слов): /addgift MY SUPER CODE 500 10"
        )
        return

    try:
        max_users_str = context.args[-1]
        amount_str = context.args[-2]
        code_name_parts = context.args[:-2]

        if not code_name_parts:
            await update.message.reply_text("❌ Название кода не может быть пустым.")
            return

        gift_code_name = " ".join(code_name_parts)
        amount = float(amount_str)
        max_users = int(max_users_str)

        if amount <= 0:
            await update.message.reply_text("❌ Сумма должна быть положительным числом.")
            return
        if max_users <= 0:
            await update.message.reply_text("❌ Число пользователей должно быть положительным.")
            return

        with get_db_connection() as conn:
            c = conn.cursor()

            c.execute('SELECT 1 FROM gift_codes WHERE code = ?', (gift_code_name,))
            if c.fetchone():
                await update.message.reply_text(
                    f"❌ Код с названием \"{escape_markdown_v2(gift_code_name)}\" уже существует\\.", # Escaped trailing dot
                    parse_mode='MarkdownV2'
                )
                return

            msk_tz = pytz.timezone('Europe/Moscow')
            c.execute('INSERT INTO gift_codes (code, amount, max_users, used_users, created_by, created_at) VALUES (?, ?, ?, 0, ?, ?)',
                      (gift_code_name, amount, max_users, update.effective_user.id, datetime.now(msk_tz).isoformat()))
            conn.commit()

            success_message_text = (
                f"✅ *Подарочный код создан*!\n\n"
                f"🏷️ Название: `{escape_markdown_v2(gift_code_name)}`\n"
                f"💰 Сумма: {amount}₽\n"
                f"👥 Макс. пользователей: {max_users}"
            )
            await update.message.reply_text(success_message_text, parse_mode='Markdown')

    except ValueError:
        await update.message.reply_text("❌ Ошибка в формате суммы или числа пользователей. Убедитесь, что это числа.")
    except Exception as e:
        logging.error(f"Error in add_gift_command: {e}", exc_info=True)
        await update.message.reply_text(
            f"❌ Произошла ошибка при создании кода: {escape_markdown_v2(str(e))}",
            parse_mode='MarkdownV2'
        )

async def check_gift_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    if not context.args:
        await update.message.reply_text("❌ Укажите код: /checkgift <код>")
        return
    code = context.args[0].strip()
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT amount, max_users, used_users, created_at FROM gift_codes WHERE code = ?', (code,))
        gift_data = c.fetchone()
        if not gift_data:
            await update.message.reply_text("❌ Код не найден.")
            return
        amount, max_users, used_users, created_at = gift_data
        created_at_formatted = await format_date(created_at)
        c.execute('SELECT u.username, ga.activated_at FROM gift_activations ga JOIN users u ON ga.user_id = u.user_id WHERE ga.code = ?', (code,))
        activations = c.fetchall()
        response = f"🎁 *Код: {code}*\n\n💰 Сумма: `{amount}₽`\n👥 Макс.пользователей: `{max_users}`\n✅ Использовано: `{used_users}`\n📅 Создано:  `{created_at_formatted}`\n\n📋 *Активации:*\n"

        if activations:
            activation_lines = []
            for username, activated_at in activations:
                formatted_date = await format_date(activated_at)
                activation_lines.append(f"👤 `{username}` — `{formatted_date}`")
            response += "\n".join(activation_lines)
        else:
            response += "Пока нет."

        await update.message.reply_text(response, parse_mode='Markdown')

async def announce(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    if not context.args:
        await update.message.reply_text("❌ Укажите сообщение: /announce <сообщение>")
        return
    message = escape_markdown_v2(" ".join(context.args))
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT user_id FROM users')
        user_ids = [row[0] for row in c.fetchall()]

    semaphore = asyncio.Semaphore(30)
    async def send_message(user_id):
        async with semaphore:
            try:
                await context.bot.send_message(user_id, message, parse_mode='MarkdownV2')
            except Exception:
                logging.error(f"Failed to send to {user_id}")

    tasks = [send_message(uid) for uid in user_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    sent_count = sum(1 for r in results if not isinstance(r, Exception))
    await update.message.reply_text(f"✅ Отправлено: {sent_count}\n❌ Не удалось: {len(user_ids) - sent_count}")

async def userinfo_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    if not context.args:
        await update.message.reply_text("❌ Укажите username, Telegram-никнейм (@username) или ID: /userinfo <username или @username или ID>")
        return
    target = context.args[0].strip()

    with get_db_connection() as conn:
        c = conn.cursor()
        user_data = None
        target_user_id = None
        username = None

        if target.isdigit():
            target_user_id = int(target)
            c.execute('SELECT username, marzban_username, trial_active, trial_end_date, subscription_active, subscription_end_date, referral_code, referred_by, balance FROM users WHERE user_id = ?', (target_user_id,))
            user_data = c.fetchone()
            if user_data:
                username, marzban_username, trial_active, trial_end_date, sub_active, sub_end_date, ref_code, referred_by, balance = user_data
        else:
            search_username = target.lstrip('@')
            c.execute('SELECT user_id, marzban_username, trial_active, trial_end_date, subscription_active, subscription_end_date, referral_code, referred_by, balance FROM users WHERE username = ?', (search_username,))
            user_data = c.fetchone()
            if user_data:
                target_user_id, marzban_username, trial_active, trial_end_date, sub_active, sub_end_date, ref_code, referred_by, balance = user_data
                username = search_username
            elif target.startswith('@'):
                c.execute('SELECT user_id, username, marzban_username, trial_active, trial_end_date, subscription_active, subscription_end_date, referral_code, referred_by, balance FROM users WHERE username = ?', (search_username,))
                user_data = c.fetchone()
                if user_data:
                    target_user_id, username, marzban_username, trial_active, trial_end_date, sub_active, sub_end_date, ref_code, referred_by, balance = user_data

        if not user_data:
            await update.message.reply_text("❌ Пользователь не найден.")
            return

        if sub_active:
            trial_active = 0
            c.execute('UPDATE users SET trial_active = 0 WHERE user_id = ?', (target_user_id,))
            conn.commit()
            logging.info(f"Disabled trial for user {target_user_id} due to active subscription")

        trial_end_formatted = await format_date(trial_end_date) if trial_end_date and trial_active else "Нет"
        sub_end_formatted = await format_date(sub_end_date) if sub_end_date and sub_active else "Нет"

        # Токен больше не нужен, так как он передается при инициализации SDK
        user_info = await user_exists(marzban_username or username)
        msk_tz = pytz.timezone('Europe/Moscow')
        current_time = datetime.now(msk_tz)

        marzban_status = "❌ Неактивна"
        marzban_expiry = "Нет данных"
        subscription_url = ""

        if user_info and hasattr(user_info, 'expire_at') and user_info.expire_at:
            marzban_expiry_dt = user_info.expire_at.astimezone(msk_tz)
            marzban_expiry = marzban_expiry_dt.strftime('%d.%m.%Y %H:%M')

            # Используем новую функцию для определения статуса
            marzban_status = get_subscription_status_admin(user_info, marzban_expiry_dt, current_time.replace(tzinfo=pytz.UTC))

            if hasattr(user_info, 'subscription_url'):
                # URL оставляем без экранирования, экранируем только текст ссылки
                subscription_url = f"\n🔗 Ссылка на подписку: [{escape_markdown_v2('Открыть')}]({user_info.subscription_url})"
        elif trial_active or sub_active:
            end_date = datetime.fromisoformat(sub_end_date if sub_active else trial_end_date).astimezone(msk_tz)
            marzban_expiry = end_date.strftime('%d.%m.%Y %H:%M')
            marzban_status = "✅ Активна" if end_date > current_time.replace(tzinfo=pytz.UTC) else "❌ Истекла"

        c.execute('SELECT COUNT(*) FROM users WHERE referred_by = ?', (target_user_id,))
        referral_count = c.fetchone()[0]
        referrer_info = "Нет"
        if referred_by:
            c.execute('SELECT username FROM users WHERE user_id = ?', (referred_by,))
            referrer_data = c.fetchone()
            if referrer_data:
                referrer_info = f"`{escape_markdown_v2(referrer_data[0])}` \\(ID\\: `{referred_by}`\\)"

        c.execute('SELECT ga.code, ga.activated_at, gc.amount FROM gift_activations ga JOIN gift_codes gc ON ga.code = gc.code WHERE ga.user_id = ?', (target_user_id,))
        gift_activations = c.fetchall()
        if gift_activations:
            gift_history_lines = []
            for code, activated_at, amount in gift_activations:
                formatted_date = await format_date(activated_at)
                gift_history_lines.append(
                    f"Код: `{escape_markdown_v2(code)}`, Сумма: `{amount}₽`, Дата: `{escape_markdown_v2(formatted_date)}`"
                )
            gift_history = "\n".join(gift_history_lines)
        else:
            gift_history = "Нет"

        # Обработка истории платежей
        try:
            c.execute('SELECT amount, payment_method, currency, timestamp FROM payment_history WHERE user_id = ?', (target_user_id,))
            payment_records = c.fetchall()
            logging.info(f"Payment records for user {target_user_id}: {payment_records}")
            if payment_records:
                payment_history_lines = []
                for record in payment_records:
                    if len(record) == 4:
                        amount, method, currency, timestamp = record
                        formatted_date = await format_date(timestamp)
                        method_display = (
                            'СБП' if method == 'sbp' else
                            'Карта' if method == 'card' else
                            'CryptoBot' if method == 'crypto' else
                            'Heleket' if method == 'crypto' else
                            'Heleket1'
                        )
                        payment_history_lines.append(
                            f"Сумма: `{amount} {escape_markdown_v2(currency)}`, Метод: `{escape_markdown_v2(method_display)}`, Дата: `{escape_markdown_v2(formatted_date)}`"
                        )
                    else:
                        logging.warning(f"Invalid payment record format for user {target_user_id}: {record}")
                payment_history = "\n".join(payment_history_lines) if payment_history_lines else "Нет записей"
            else:
                payment_history = "Нет"
        except Exception as e:
            logging.error(f"Database error while fetching payment history for user {target_user_id}: {e}", exc_info=True)
            payment_history = "Ошибка при загрузке истории платежей"

        # Обработка истории подписок
        try:
            c.execute('SELECT period, amount, purchase_date, end_date FROM subscription_history WHERE user_id = ? ORDER BY purchase_date DESC', (target_user_id,))
            subscription_records = c.fetchall()
            if subscription_records:
                subscription_history_lines = []
                for period, amount, purchase_date, end_date in subscription_records:
                    purchase_formatted = await format_date(purchase_date)
                    end_formatted = await format_date(end_date)
                    subscription_history_lines.append(
                        f"Тариф: `{escape_markdown_v2(period)}`, Сумма: `{amount}₽`, Куплено: `{escape_markdown_v2(purchase_formatted)}`, До: `{escape_markdown_v2(end_formatted)}`"
                    )
                subscription_history = "\n".join(subscription_history_lines)
            else:
                subscription_history = "Нет"
        except Exception as e:
            logging.error(f"Database error while fetching subscription history for user {target_user_id}: {e}", exc_info=True)
            subscription_history = "Ошибка при загрузке истории подписок"

        # Формирование ответа с экранированием
        response = (
            f"👤 *Пользователь*\n\n"
            f"🆔 ID: `{target_user_id}`\n"
            f"👤 Никнейм: `@{escape_markdown_v2(username or 'Не указано')}`\n"
            f"💰 Баланс: `{balance:.2f}₽`\n"
            f"👥 Рефералов: `{referral_count}`\n"
            f"🔗 Реф\\.код: `{escape_markdown_v2(ref_code or 'Нет')}`\n"
            f"👤 Приглашен: {referrer_info}\n\n"
            f"📊 *Статус в боте:*\n"
            f"Пробная: `{ '✅' if trial_active else '❌'}`\n"
            f"До: `{escape_markdown_v2(trial_end_formatted)}`\n"
            f"Платная: `{ '✅' if sub_active else '❌'}`\n"
            f"До: `{escape_markdown_v2(sub_end_formatted)}`\n\n"
            f"📡 *Remnawave:*\n"
            f"Имя: `{escape_markdown_v2(marzban_username or 'Не задано')}`\n"
            f"Состояние: `{escape_markdown_v2(marzban_status)}`\n"
            f"Окончание: `{escape_markdown_v2(marzban_expiry)}`{subscription_url}\n\n"
            f"💳 *Подарочные коды:*\n{gift_history}\n\n"
            f"💸 *Платежи:*\n{payment_history}\n\n"
            f"📋 *История подписок:*\n{subscription_history}"
        )

        try:
            await update.message.reply_text(response, parse_mode='MarkdownV2')
        except BadRequest as e:
            logging.error(f"Failed to send message due to Markdown parsing error: {e}, response: {response}, subscription_url: {user_info.subscription_url if user_info and hasattr(user_info, 'subscription_url') else 'None'}")
            # Отправляем сообщение без Markdown в случае ошибки
            fallback_response = (
                f"👤 Пользователь\n\n"
                f"🆔 ID: {target_user_id}\n"
                f"👤 Никнейм: @{username or 'Не указано'}\n"
                f"💰 Баланс: {balance:.2f}₽\n"
                f"👥 Рефералов: {referral_count}\n"
                f"🔗 Реф.код: {ref_code or 'Нет'}\n"
                f"👤 Приглашен: {referrer_info.replace('`', '')}\n\n"
                f"📊 Статус в боте:\n"
                f"Пробная: {'✅' if trial_active else '❌'}\n"
                f"До: {trial_end_formatted}\n"
                f"Платная: {'✅' if sub_active else '❌'}\n"
                f"До: {sub_end_formatted}\n\n"
                f"📡 Remnawave:\n"
                f"Имя: {marzban_username or 'Не задано'}\n"
                f"Состояние: {marzban_status}\n"
                f"Окончание: {marzban_expiry}\n"
                f"🔗 Ссылка на подписку: {user_info.subscription_url if user_info and hasattr(user_info, 'subscription_url') else 'Нет'}\n\n"
                f"💳 Подарочные коды:\n{gift_history.replace('`', '')}\n\n"
                f"💸 Платежи:\n{payment_history.replace('`', '')}\n\n"
                f"📋 История подписок:\n{subscription_history.replace('`', '')}"
            )
            await update.message.reply_text(fallback_response)

async def stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    with get_db_connection() as conn:
        c = conn.cursor()
        try:
            c.execute('SELECT COUNT(*) FROM users')
            total_users = c.fetchone()[0]
            c.execute('SELECT COUNT(*) FROM users WHERE subscription_active = 1 OR trial_active = 1')
            active_subscriptions = c.fetchone()[0]
            c.execute('SELECT COUNT(*) FROM users WHERE subscription_active = 1')
            paid_subscriptions = c.fetchone()[0]
            c.execute('SELECT COUNT(*) FROM users WHERE trial_active = 1')
            trial_subscriptions = c.fetchone()[0]
            c.execute('SELECT SUM(balance) FROM users WHERE balance > 0')
            total_balance = c.fetchone()[0] or 0.0
            c.execute('SELECT COUNT(*) FROM users WHERE referred_by IS NOT NULL')
            referred_users = c.fetchone()[0]

            c.execute('SELECT SUM(amount), COUNT(*) FROM payment_history WHERE payment_method = "sbp"')
            sbp_result = c.fetchone()
            sbp_total, sbp_count = sbp_result if sbp_result else (0.0, 0)
            sbp_total = sbp_total or 0.0

            c.execute('SELECT SUM(amount), COUNT(*) FROM payment_history WHERE payment_method = "crypto"')
            crypto_result = c.fetchone()
            crypto_total, crypto_count = crypto_result if crypto_result else (0.0, 0)
            crypto_total = (crypto_total or 0.0) * 90.0

            c.execute('SELECT SUM(amount), COUNT(*) FROM payment_history WHERE payment_method = "heleket"')
            heleket_result = c.fetchone()
            heleket_total, heleket_count = heleket_result if heleket_result else (0.0, 0)
            heleket_total = (heleket_total or 0.0) * 90.0

            c.execute('SELECT SUM(amount), COUNT(*) FROM payment_history WHERE payment_method = "card"')
            card_result = c.fetchone()
            card_total, card_count = card_result if card_result else (0.0, 0)
            card_total = card_total or 0.0

            c.execute('SELECT COUNT(*), SUM(amount), SUM(used_users) FROM gift_codes')
            gift_result = c.fetchone()
            gift_codes_count, gift_codes_amount, gift_codes_used = gift_result if gift_result else (0, 0.0, 0)
            gift_codes_amount = gift_codes_amount or 0.0
            gift_codes_used = gift_codes_used or 0

            c.execute('SELECT COUNT(*) FROM gift_activations')
            gift_activations = c.fetchone()[0]

            c.execute('SELECT user_id, username, balance FROM users WHERE balance > 0 ORDER BY balance DESC LIMIT 3')
            top_users = c.fetchall()

            msk_tz = pytz.timezone('Europe/Moscow')
            top_users_str = "\n\n🏆 *Топ-3 по балансу:*\n"
            if top_users:
                for i, (user_id, username, balance) in enumerate(top_users, 1):
                    username_display = f"@{username}" if username else f"ID: {user_id}"
                    top_users_str += f"{i}. {username_display} — `{balance:.2f}₽`\n"
            else:
                top_users_str += "Нет пользователей с положительным балансом."

            response = (
                f"📊 *Статистика*\n\n👥 Зарегистрировано: `{total_users}`\n📈 Подписок: `{active_subscriptions}`\n├─ Платных: `{paid_subscriptions}`\n└─ Пробных: `{trial_subscriptions}`\n"
                f"💰 Общий баланс: `{total_balance:.2f}₽`\n👥 Приглашенных: `{referred_users}`\n\n💳 *Платежи:*\nСБП: `{sbp_count}` на `{sbp_total:.2f}₽`\nCryptoBot: `{crypto_count}` на `{crypto_total:.2f}₽`\nHeleket: `{heleket_count}` на `{heleket_total:.2f}₽`\nКарта: `{card_count}` на `{card_total:.2f}₽`\n"
                f"Общий доход: `{sbp_total + crypto_total + heleket_total + card_total:.2f}₽`\n\n🎁 *Коды:*\nСоздано: `{gift_codes_count}`\nСумма: `{gift_codes_amount:.2f}₽`\nАктиваций: `{gift_activations}`\n"
                f"Использовано: `{gift_codes_used}`\nДата: `{datetime.now(msk_tz).strftime('%d.%m.%Y %H:%M')}`"
                f"{top_users_str}"
            )
            await update.message.reply_text(response, parse_mode='Markdown')
        except Exception as e:
            logging.error(f"Database error in stats_command: {e}", exc_info=True)
            await update.message.reply_text("❌ Ошибка при получении статистики.")

async def reset_balance_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return

    if not context.args:
        await update.message.reply_text("❌ Укажите ID пользователя: /reset_balance <user_id>")
        return

    try:
        target_user_id = int(context.args[0])
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT balance, username FROM users WHERE user_id = ?', (target_user_id,))
            user_data = c.fetchone()

            if not user_data:
                await update.message.reply_text(f"❌ Пользователь с ID `{target_user_id}` не найден.")
                return

            current_balance, username = user_data
            if current_balance == 0.0:
                await update.message.reply_text(f"✅ Баланс пользователя `{target_user_id}` уже равен 0₽.")
                return

            c.execute('UPDATE users SET balance = 0.0 WHERE user_id = ?', (target_user_id,))
            conn.commit()

            username_display = f"@{username}" if username else f"ID: {target_user_id}"
            await update.message.reply_text(
                f"✅ Баланс пользователя {username_display} обнулён. Предыдущий баланс: `{current_balance:.2f}₽`."
            )
            logging.info(f"Admin {update.effective_user.id} reset balance for user {target_user_id} from {current_balance} to 0.")

    except ValueError:
        await update.message.reply_text("❌ ID должен быть числом: /reset_balance <user_id>")
    except Exception as e:
        await update.message.reply_text(f"❌ Ошибка при обнулении баланса: {e}")
        logging.error(f"Error resetting balance for user {target_user_id}: {e}")

async def sync(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.effective_user:
        logging.error("No effective user in update object")
        await update.message.reply_text("❌ Ошибка: нет данных о пользователе.")
        return

    user_id = update.effective_user.id
    if user_id not in ADMIN_IDS:
        logging.warning(f"Unauthorized sync attempt by user_id {user_id}")
        await update.message.reply_text("❌ У вас нет прав для выполнения этой команды.")
        return

    # Проверяем, указан ли user_id для синхронизации
    args = context.args
    if not args or len(args) != 1:
        await update.message.reply_text("❌ Использование: /sync <user_id>")
        return

    try:
        sync_user_id = int(args[0])
    except ValueError:
        await update.message.reply_text("❌ Ошибка: user_id должен быть числом.")
        return

    try:
        # Вызываем функцию синхронизации
        result = await sync_user_with_remnawave(sync_user_id)
        await update.message.reply_text(result)
    except Exception as e:
        logging.error(f"Error syncing user {sync_user_id}: {e}")
        await update.message.reply_text(f"❌ Ошибка при синхронизации: {e}")

def get_days_string(num_days: int) -> str:
    """
    Возвращает строку с числом дней и правильным склонением слова 'день'.
    Например: 1 день, 2 дня, 5 дней.
    """
    if num_days % 10 == 1 and num_days % 100 != 11:
        return f"{num_days} день"
    elif 2 <= num_days % 10 <= 4 and (num_days % 100 < 10 or num_days % 100 >= 20):
        return f"{num_days} дня"
    else:
        return f"{num_days} дней"

async def add_days_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return

    if len(context.args) != 2:
        await update.message.reply_text("❌ Формат: /add_days <user_id> <количество_дней>")
        return

    try:
        user_id = int(context.args[0])
        days_to_add = int(context.args[1])

        if days_to_add <= 0:
            await update.message.reply_text("❌ Количество дней должно быть положительным числом.")
            return

        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT username, marzban_username FROM users WHERE user_id = ?', (user_id,))
            user_data = c.fetchone()

            if not user_data:
                await update.message.reply_text(f"❌ Пользователь с ID {user_id} не найден.")
                return

            username, marzban_username_db = user_data # Переименовал marzban_username во избежание путаницы

            # Для отображения админу:
            # Если username есть, используем его, иначе ID. Экранируем на всякий случай.
            if username:
                username_display_admin = escape_markdown_v2(f"@{username}")
            else:
                username_display_admin = f"ID: `{user_id}`" # ID можно вставить в `code` без экранирования

            effective_marzban_username = marzban_username_db
            if not effective_marzban_username:
                effective_marzban_username = f"user_{user_id}" # Это имя не будет отображаться, оно для API
                logging.info(f"No marzban_username found for user {user_id}, using default: {effective_marzban_username}")

        success, new_expire_obj, updated_user = await extend_remnawave_subscription(user_id, effective_marzban_username, days_to_add)

        if success:
            new_expire_dt = None
            if isinstance(new_expire_obj, (int, float)):
                new_expire_dt = datetime.fromtimestamp(new_expire_obj, tz=pytz.timezone('Europe/Moscow'))
            elif isinstance(new_expire_obj, datetime):
                if new_expire_obj.tzinfo is None or new_expire_obj.tzinfo.utcoffset(new_expire_obj) is None:
                    new_expire_dt = pytz.timezone('Europe/Moscow').localize(new_expire_obj)
                else:
                    new_expire_dt = new_expire_obj.astimezone(pytz.timezone('Europe/Moscow'))

            if not new_expire_dt:
                # Если не смогли определить дату, это проблема
                logging.error(f"Could not determine new_expire_dt for user {user_id}. new_expire_obj: {new_expire_obj}")
                await update.message.reply_text(f"❌ Ошибка при обработке даты продления для пользователя {username_display_admin}\\.")
                return

            admin_formatted_date = new_expire_dt.strftime('%d.%m.%Y %H:%M')
            user_formatted_date = new_expire_dt.strftime('%d.%m.%Y, %H:%M') # Формат для пользователя

            subscription_url_md = ""
            if updated_user and hasattr(updated_user, 'subscription_url') and updated_user.subscription_url:
                # URL в MarkdownV2 ссылке [текст](URL) не нужно экранировать сам URL
                # Экранируем только текст ссылки, если он динамический
                link_text = escape_markdown_v2("Открыть ссылку")
                subscription_url_md = f"\n🔗 [*{link_text}*]({updated_user.subscription_url})"


            days_str = get_days_string(days_to_add)

            # Отправляем сообщение админу
            admin_message = (
                f"✅ Подписка пользователя {username_display_admin} продлена на {escape_markdown_v2(days_str)}\\.\\n"
                f"📅 Новая дата окончания: `{admin_formatted_date}`{subscription_url_md}"
            )
            await update.message.reply_text(admin_message, parse_mode='MarkdownV2')

            # Добавляем запись в историю подписок
            try:
                end_date_for_history = new_expire_dt # Уже в нужной таймзоне
                # Описание для истории тоже лучше экранировать, если оно содержит что-то кроме простых слов
                history_description = (f"Дополнительно {days_str}")
                add_subscription_history(user_id, history_description, 0, end_date_for_history)
                logging.info(f"Added subscription history for user {user_id}: +{days_to_add} days by admin")
            except Exception as e:
                logging.error(f"Failed to add subscription history for user {user_id}: {e}")

            # Отправляем уведомление пользователю (Вариант 2)
            user_message_text = f"""🥳 *Ваша подписка стала еще длиннее\\!*

Мы рады сообщить, что ваша подписка была успешно продлена\\.

✅ *Срок продления:* \\+{escape_markdown_v2(days_str)}
🗓️ *Действует до:* `{user_formatted_date}` \\(МСК\\)

Приятного использования\\! 🚀
"""
            try:
                await context.bot.send_message(
                    user_id,
                    user_message_text,
                    parse_mode='MarkdownV2',
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
            except Exception as e:
                logging.error(f"Failed to send notification to user {user_id}: {e}")
        else:
            error_message = escape_markdown_v2(str(new_expire_obj)) # Экранируем сообщение об ошибке
            await update.message.reply_text(f"❌ Ошибка при продлении подписки: {error_message}", parse_mode='MarkdownV2')

    except ValueError:
        await update.message.reply_text("❌ ID пользователя и количество дней должны быть целыми числами.")
    except Exception as e:
        logging.error(f"Error in add_days_command: {e}", exc_info=True)
        error_message_general = escape_markdown_v2(str(e))
        await update.message.reply_text(f"❌ Произошла ошибка: {error_message_general}", parse_mode='MarkdownV2')

async def test_api_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Тестирует подключение к Remnawave API"""
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return

    await update.message.reply_text("🔄 Тестирую подключение к Remnawave API...")

    success = await test_remnawave_connection()
    if success:
        await update.message.reply_text("✅ Подключение к Remnawave API работает!")
    else:
        await update.message.reply_text("❌ Ошибка подключения к Remnawave API. Проверьте логи.")

async def backupdb_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⛔️ Нет прав.")
        return
    await update.message.reply_text("⏳ Создаю и отправляю бэкап базы...")
    try:
        await send_db_backup_manual(context.bot)
        await update.message.reply_text("✅ Бэкап отправлен администраторам.")
    except Exception as e:
        await update.message.reply_text(f"❌ Ошибка при создании или отправке бэкапа: {e}")

async def is_admin(user_id):
    """Проверяет, является ли пользователь администратором"""
    return user_id in ADMIN_IDS

async def sync_remnawave_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Обработчик команды /sync_remnawave - синхронизирует пользователей из Remnawave с базой данных
    """
    user = update.effective_user
    
    if not await is_admin(user.id):
        await update.message.reply_text("⛔️ У вас нет прав для выполнения этой команды.")
        return
    
    # Проверяем подключение к Remnawave API перед синхронизацией
    await update.message.reply_text("🔄 Проверка соединения с Remnawave API...")
    
    connection_ok = await test_remnawave_connection()
    if not connection_ok:
        await update.message.reply_text("❌ Не удалось подключиться к Remnawave API. Синхронизация отменена.")
        return
    
    # Проверяем конфигурацию inbound'ов перед синхронизацией
    inbounds_ok, inbounds_message = await test_inbound_configuration()
    if not inbounds_ok:
        await update.message.reply_text(f"❌ Проблема с конфигурацией inbound'ов:\n\n{inbounds_message}\n\nСинхронизация отменена.")
        return

    await update.message.reply_text("🔄 Запуск синхронизации пользователей из Remnawave. Это может занять некоторое время...")
    
    try:
        # Запускаем процесс синхронизации
        stats = await sync_all_remnawave_users_with_database()
        
        # Отправляем краткий отчет
        await update.message.reply_text(
            f"{stats['summary']}\n\n"
            f"Подробная информация отправлена отдельным сообщением.",
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Отправляем подробный отчет в виде отдельного сообщения
        if stats["details"]:
            # Если деталей много, разбиваем их на части, чтобы избежать превышения лимита длины сообщения
            details_chunks = []
            current_chunk = []
            current_length = 0
            
            for detail in stats["details"]:
                if current_length + len(detail) + 1 > 3800:  # Оставляем небольшой запас
                    details_chunks.append("\n".join(current_chunk))
                    current_chunk = [detail]
                    current_length = len(detail)
                else:
                    current_chunk.append(detail)
                    current_length += len(detail) + 1  # +1 для символа новой строки
            
            if current_chunk:
                details_chunks.append("\n".join(current_chunk))
            
            # Отправляем каждый чанк как отдельное сообщение
            for i, chunk in enumerate(details_chunks):
                header = f"📋 Подробная информация (часть {i+1}/{len(details_chunks)}):\n\n"
                await update.message.reply_text(header + chunk)
    except Exception as e:
        logging.error(f"Error during sync_remnawave_command: {e}")
        await update.message.reply_text(f"❌ Произошла ошибка при синхронизации: {str(e)}")