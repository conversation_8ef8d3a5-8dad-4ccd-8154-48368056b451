import logging
import pytz
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from database import get_db_connection, add_balance, get_user_balance, add_referral_earning
from utils import format_date
from handlers.states import user_states, payment_messages
from handlers.keyboard_utils import get_main_keyboard
from payment_utils import create_payment_link, create_crypto_invoice, create_heleket_invoice, create_card_payment_link

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.effective_user:  # Проверка на наличие пользователя
        logging.error("No effective user in update object")
        if update.message:
            await update.message.reply_text(
                "❌ Произошла ошибка при обработке команды. Попробуйте позже."
            )
        return

    user_id = update.effective_user.id
    telegram_username = update.effective_user.username or f"user_{user_id}"
    referral_code = context.args[0] if context.args else None

    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT username, referral_code, referred_by, marzban_username FROM users WHERE user_id = ?', (user_id,))
        existing_user = c.fetchone()

        if existing_user:
            db_username, db_ref_code, db_referred_by, db_marzban_username = existing_user
            if referral_code:
                await update.message.reply_text(
                    "⚠️ *Вы уже зарегистрированы\\!*\n\n"
                    "Вы не можете использовать реферальную ссылку, так как уже являетесь пользователем бота\\.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                    ]),
                    parse_mode='MarkdownV2'
                )

            # Обновляем username, если изменился
            if db_username != telegram_username:
                c.execute('UPDATE users SET username = ? WHERE user_id = ?', (telegram_username, user_id))
                conn.commit()
                logging.info(f"Updated username for user_id {user_id}: {db_username} -> {telegram_username}")

            await update.message.reply_photo(
                photo="https://i.imgur.com/pN0xMho.png",
                caption="👋 *Добро пожаловать в SPACE VPN\\!*\n\n"
                        "*Наши локации:*\n"
                        "🇫🇮 Финляндия\n"
                        "🇩🇪 Германия\n"
                        "🇺🇸 Америка\n\n"
                        ">*Ошибка в боте? Нашел баг?*\n"
                        ">Пиши: @spacevpn\\_help\n\n"
                        "👇 *Выберите опцию:*",
                reply_markup=get_main_keyboard(user_id),
                parse_mode='MarkdownV2'
            )
            return

        # Регистрация нового пользователя
        username = telegram_username
        new_ref_code = f"ref{user_id}"
        # Задаём marzban_username как user_<user_id> для новых пользователей
        marzban_username = f"user_{user_id}"
        if referral_code:
            c.execute('SELECT user_id FROM users WHERE referral_code = ?', (referral_code,))
            referrer = c.fetchone()
            if referrer and referrer[0] != user_id:
                c.execute('INSERT INTO users (user_id, username, marzban_username, referral_code, referred_by, balance) VALUES (?, ?, ?, ?, ?, 0.0)',
                          (user_id, username, marzban_username, new_ref_code, referrer[0]))
                conn.commit()
                if add_balance(referrer[0], 25):
                    # Записываем информацию о начислении за регистрацию
                    add_referral_earning(referrer[0], user_id, 25, 'registration')
                    try:
                        await context.bot.send_message(
                            referrer[0],
                            "🎉 Новый реферал\\! Твой баланс пополнен на +25₽",
                            reply_markup=InlineKeyboardMarkup([
                                [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                            ]),
                            parse_mode='MarkdownV2'
                        )
                    except Exception as e:
                        logging.error(f"Failed to notify referrer {referrer[0]}: {e}")
        else:
            c.execute('INSERT INTO users (user_id, username, marzban_username, referral_code, balance) VALUES (?, ?, ?, ?, 0.0)',
                      (user_id, username, marzban_username, new_ref_code))
            conn.commit()
        logging.info(f"Registered new user: user_id={user_id}, username={username}, marzban_username={marzban_username}")

    await update.message.reply_photo(
        photo="https://i.imgur.com/pN0xMho.png",
        caption="👋 *Добро пожаловать в SPACE VPN\\!*\n\n"
                "*Наши локации:*\n"
                "🇫🇮 Финляндия\n"
                "🇩🇪 Германия\n"
                "🇺🇸 Америка\n\n"
                ">*Ошибка в боте? Нашел баг?*\n"
                ">Пиши: @spacevpn\\_help\n\n"
                "👇 *Выберите опцию:*",
        reply_markup=get_main_keyboard(user_id),
        parse_mode='MarkdownV2'
    )

async def gift_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    if not context.args:
        await update.message.reply_text("❌ Укажите код: /gift <код>")
        return
    code = context.args[0].strip()
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT amount, max_users, used_users FROM gift_codes WHERE code = ?', (code,))
        gift_data = c.fetchone()
        if not gift_data or gift_data[2] >= gift_data[1]:
            await update.message.reply_text("❌ Код не найден или исчерпан.")
            return
        c.execute('SELECT user_id FROM gift_activations WHERE code = ? AND user_id = ?', (code, user_id))
        if c.fetchone():
            await update.message.reply_text("❌ Вы уже активировали этот код.")
            return
        if add_balance(user_id, gift_data[0]):
            msk_tz = pytz.timezone('Europe/Moscow')
            c.execute('INSERT INTO gift_activations (code, user_id, activated_at) VALUES (?, ?, ?)',
                      (code, user_id, datetime.now(msk_tz).isoformat()))
            c.execute('UPDATE gift_codes SET used_users = used_users + 1 WHERE code = ?', (code,))
            conn.commit()
            await update.message.reply_text(f"✅ Код активирован! +{gift_data[0]}₽")
        else:
            await update.message.reply_text("❌ Ошибка активации.")

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.message:
        return

    user_id = update.effective_user.id

    # Если сообщение от админа и начинается с /
    if user_id in [5643973561] and update.message.text and update.message.text.startswith('/'):
        return

    if user_id not in user_states:
        return

    try:
        # Проверяем, что ввод является числом
        amount = float(update.message.text)

        # Округляем до двух знаков после запятой
        amount = round(amount, 2)

        # Проверки для каждого метода оплаты
        if user_states[user_id] == "waiting_for_payment_amount_sbp":
            if amount < 10 or amount > 100000:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть от 10 до 100000₽.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            if amount <= 0:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть больше 0.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            payment_link = await create_payment_link(amount, user_id)
            payment_type = "sbp"
            currency = "₽"
        elif user_states[user_id] == "waiting_for_payment_amount_card":
            if amount < 10 or amount > 100000:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть от 10 до 100000₽.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            if amount <= 0:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть больше 0.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            payment_link = await create_card_payment_link(amount, user_id)
            payment_type = "card"
            currency = "₽"
        elif user_states[user_id] == "waiting_for_payment_amount_crypto":
            if amount < 1 or amount > 1000:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть от 1 до 1000 USDT.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            if amount <= 0:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть больше 0.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            payment_link, _ = await create_crypto_invoice(amount, user_id)
            payment_type = "crypto"
            currency = "USDT"
        elif user_states[user_id] == "waiting_for_payment_amount_heleket":
            if amount < 1 or amount > 1000:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть от 1 до 1000 USDT.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            if amount <= 0:
                error_msg = await update.message.reply_text(
                    "❌ Сумма должна быть больше 0.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                )
                # Удаляем сообщение пользователя с неверным вводом
                try:
                    await context.bot.delete_message(update.message.chat_id, update.message.message_id)
                except Exception as e:
                    logging.error(f"Error deleting user message: {e}")
                return
            payment_link, _ = await create_heleket_invoice(amount, user_id)
            payment_type = "heleket"
            currency = "USDT"
        else:
            return

        if payment_link:
            # Перед созданием нового сообщения об оплате очищаем предыдущие состояния пользователя
            # чтобы избежать перетекания данных от предыдущих попыток оплаты
            for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                        f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                        f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
                if key in payment_messages:
                    del payment_messages[key]

            initial_balance = await get_user_balance(user_id)
            user_states[f"{user_id}_initial_balance"] = initial_balance
            user_states[f"{user_id}_amount"] = amount

            msg = await context.bot.send_message(
                update.message.chat_id,
                f"💳 *Пополнение*\n\n⏳ Ожидаем `{amount:.2f}{currency}`...\nБаланс: `{initial_balance:.2f}₽`",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("Оплатить", url=payment_link)],
                    [InlineKeyboardButton("✅ Я оплатил", callback_data='payment_verification')],
                    [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                ]),
                parse_mode='Markdown'
            )

            # Инициализируем только базовые данные, время проверки инициализируем только при нажатии на "Я оплатил"
            payment_messages[user_id] = msg.message_id
            payment_messages[f"{user_id}_url"] = payment_link
            payment_messages[f"{user_id}_type"] = payment_type

            await context.bot.delete_message(update.message.chat_id, update.message.message_id)

            # Успешно создали запрос на оплату, теперь можно удалить состояние ожидания ввода суммы
            if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
                del user_states[user_id]
        else:
            await update.message.reply_text(
                "❌ Временно оплата недоступна. Обратитесь в поддержку @spacevpn_help.",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
            )
    except ValueError:
        # Отправляем сообщение об ошибке
        error_msg = await update.message.reply_text(
            "❌ Введите корректное число (например, 100.50).",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
        )

        # Удаляем сообщение пользователя с неверным вводом
        try:
            await context.bot.delete_message(update.message.chat_id, update.message.message_id)
        except Exception as e:
            logging.error(f"Error deleting user message after ValueError: {e}")