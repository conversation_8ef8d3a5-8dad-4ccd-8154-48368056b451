import logging
import asyncio
import pytz
from datetime import datetime, timedelta
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Bo<PERSON>
from telegram.error import BadRequest, Forbidden
from database import get_db_connection, add_notification_record, check_notification_sent, count_failed_notification_attempts
from remnawave_utils import get_remnawave_users_with_telegram_id
from utils import format_time_left

# Константы для временных интервалов уведомлений
NOTIFICATION_72H = 72  # 3 дня
NOTIFICATION_24H = 24  # 24 часа
NOTIFICATION_10H = 10  # 10 часов
NOTIFICATION_EXPIRED = 0  # Истекла

# Константы для параметров системы уведомлений
NOTIFICATION_RETRY_DELAY = 5  # Секунды ожидания между отправками (для избежания флуд-контроля)
MAX_MESSAGES_PER_BATCH = 20  # Максимальное количество сообщений в одном пакете

# Константа для повторной отправки уведомлений
NOTIFICATION_RESEND_HOURS = 24  # Не отправлять повторно одинаковое уведомление в течение 24 часов

async def send_expiration_warnings(bot):
    """
    Отправляет уведомления пользователям с истекающей подпиской или триалом.
    Реализует многоуровневую систему уведомлений:
    - За 3 дня до окончания
    - За 24 часа до окончания
    - За 10 часов до окончания
    - При истечении подписки
    
    Проверяет как данные из базы данных, так и актуальные данные из Remnawave.
    """
    try:
        logging.info("🔔 Запуск обработки уведомлений")
        
        # Получаем пользователей из локальной базы данных
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''
                SELECT user_id, subscription_end_date, trial_end_date, subscription_active, trial_active, expiry_notified
                FROM users
                WHERE subscription_active = 1 OR trial_active = 1
            ''')
            db_users = c.fetchall()
            
            # Создаем словарь для более быстрого доступа к данным
            db_users_dict = {user[0]: user for user in db_users}
            
        logging.info(f"Найдено {len(db_users)} активных подписок в базе данных")
        
        # Получаем пользователей из Remnawave для обогащения данных
        # и добавления пользователей, которых нет в базе данных
        remnawave_users = await get_remnawave_users_with_telegram_id()
        logging.info(f"Получено {len(remnawave_users)} пользователей из Remnawave с Telegram ID")
        
        # Обрабатываем пользователей из Remnawave для обогащения данных
        for rw_user in remnawave_users:
            telegram_id = rw_user["telegram_id"]
            expire_at = rw_user["expire_at"]
            is_active = rw_user["is_active"]
            
            # Добавляем пользователя в локальную базу, если его там нет
            if telegram_id not in db_users_dict and expire_at and is_active:
                with get_db_connection() as conn:
                    c = conn.cursor()
                    # Проверяем, существует ли пользователь в базе
                    c.execute('SELECT user_id FROM users WHERE user_id = ?', (telegram_id,))
                    if not c.fetchone():
                        # Создаем нового пользователя
                        c.execute('''
                            INSERT INTO users 
                            (user_id, username, marzban_username, subscription_active, subscription_end_date, referral_code, balance, expiry_notified)
                            VALUES (?, ?, ?, ?, ?, ?, 0.0, 0)
                        ''', (telegram_id, rw_user["username"], rw_user["username"], 1, expire_at.isoformat(), f"ref{telegram_id}"))
                        logging.info(f"Создан новый пользователь из Remnawave: {telegram_id} (username: {rw_user['username']})")
                    else:
                        # Обновляем данные существующего пользователя
                        c.execute('''
                            UPDATE users SET 
                                marzban_username = ?,
                                subscription_active = 1,
                                subscription_end_date = ?
                            WHERE user_id = ?
                        ''', (rw_user["username"], expire_at.isoformat(), telegram_id))
                        logging.info(f"Обновлен пользователь из Remnawave: {telegram_id} (username: {rw_user['username']})")
                    conn.commit()
                
                # Добавляем пользователя в словарь для дальнейшей обработки
                subscription_end_date = expire_at.isoformat()
                db_users_dict[telegram_id] = (telegram_id, subscription_end_date, None, 1, 0, 0)

        # Если после обогащения данными из Remnawave пользователей всё равно нет, 
        # то нет необходимости продолжать
        if not db_users_dict:
            logging.info("Нет активных подписок или триалов для уведомления.")
            return

        # Преобразуем словарь обратно в список
        users = list(db_users_dict.values())

        # Группы пользователей для различных уведомлений
        users_72h = []
        users_24h = []
        users_10h = []
        users_expired = []

        msk_tz = pytz.timezone('Europe/Moscow')
        now = datetime.now(msk_tz)
        
        for user in users:
            user_id, sub_end, trial_end, sub_active, trial_active, expiry_notified = user
            
            end_date = None
            if sub_active and sub_end:
                end_date = datetime.fromisoformat(sub_end)
            elif trial_active and trial_end:
                end_date = datetime.fromisoformat(trial_end)
                
            if not end_date:
                continue
                
            if end_date.tzinfo is None:
                end_date = msk_tz.localize(end_date)
                
            time_left = (end_date - now).total_seconds() / 3600  # Часы до истечения
            
            # Проверка и распределение по группам с учетом истории уведомлений
            
            # 3 дня
            if NOTIFICATION_72H > time_left > NOTIFICATION_24H and expiry_notified < 1:
                # Проверяем, отправляли ли уже это уведомление
                if not check_notification_sent(user_id, "3_дня", end_date.isoformat(), NOTIFICATION_RESEND_HOURS):
                    users_72h.append((user_id, end_date, time_left, expiry_notified))
            
            # 24 часа
            elif NOTIFICATION_24H > time_left > NOTIFICATION_10H and expiry_notified < 2:
                if not check_notification_sent(user_id, "24_часа", end_date.isoformat(), NOTIFICATION_RESEND_HOURS):
                    users_24h.append((user_id, end_date, time_left, expiry_notified))
            
            # 10 часов
            elif NOTIFICATION_10H > time_left > NOTIFICATION_EXPIRED and expiry_notified < 3:
                if not check_notification_sent(user_id, "10_часов", end_date.isoformat(), NOTIFICATION_RESEND_HOURS):
                    users_10h.append((user_id, end_date, time_left, expiry_notified))
            
            # Истекла
            elif time_left <= NOTIFICATION_EXPIRED and expiry_notified < 4:
                if not check_notification_sent(user_id, "истекла", end_date.isoformat(), NOTIFICATION_RESEND_HOURS):
                    users_expired.append((user_id, end_date, time_left, expiry_notified))

        # Статистика запланированных уведомлений
        total_notifications = len(users_72h) + len(users_24h) + len(users_10h) + len(users_expired)
        logging.info(f"Запланированы уведомления: всего {total_notifications} "
                     f"(3 дня: {len(users_72h)}, 24ч: {len(users_24h)}, 10ч: {len(users_10h)}, истекли: {len(users_expired)})")

        # Отправляем уведомления группами
        await process_notification_group(bot, users_72h, 1, "3_дня")
        await process_notification_group(bot, users_24h, 2, "24_часа")
        await process_notification_group(bot, users_10h, 3, "10_часов")
        await process_notification_group(bot, users_expired, 4, "истекла")
        
        logging.info("✅ Обработка уведомлений завершена")
        
    except Exception as e:
        logging.error(f"Ошибка в send_expiration_warnings: {e}", exc_info=True)

async def process_notification_group(bot, users, notification_level, notification_type):
    """
    Обрабатывает группу пользователей для отправки уведомлений определенного типа
    """
    if not users:
        return
    
    logging.info(f"Отправка уведомлений типа '{notification_type}' для {len(users)} пользователей")
    
    # Разбиваем на небольшие группы, чтобы избежать перегрузки
    for i in range(0, len(users), MAX_MESSAGES_PER_BATCH):
        batch = users[i:i + MAX_MESSAGES_PER_BATCH]
        for user_id, end_date, time_left, _ in batch:
            try:
                # Отправляем уведомление
                sent = await send_notification_message(bot, user_id, end_date, time_left, notification_type)
                
                # Фиксируем результат отправки в истории уведомлений
                add_notification_record(user_id, notification_type, end_date.isoformat(), 1 if sent else 0)
                
                if sent:
                    # Обновление статуса уведомлений в базе данных
                    with get_db_connection() as conn:
                        c = conn.cursor()
                        c.execute('UPDATE users SET expiry_notified = ? WHERE user_id = ?', 
                                 (notification_level, user_id))
                        conn.commit()
                    
                    # Деактивация подписки если истекла
                    if notification_type == "истекла":
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('UPDATE users SET subscription_active = 0, trial_active = 0 WHERE user_id = ?', 
                                     (user_id,))
                            conn.commit()
                
                # Пауза между отправками сообщений, чтобы избежать флуд-контроля
                await asyncio.sleep(NOTIFICATION_RETRY_DELAY)
                
            except Forbidden as e:
                error_message = str(e).lower()
                if "user is deactivated" in error_message:
                    reason = "user_deactivated"
                    logging.warning(f"Пользователь {user_id} деактивирован")
                elif "bot was blocked" in error_message:
                    reason = "bot_blocked"
                    logging.warning(f"Пользователь {user_id} заблокировал бота")
                else:
                    reason = "forbidden_error"
                    logging.warning(f"Ошибка доступа для пользователя {user_id}: {e}")
                
                # Записываем неудачную попытку в историю
                add_notification_record(user_id, notification_type, end_date.isoformat(), 0)
                
                # Проверяем количество неудачных попыток
                failed_attempts = count_failed_notification_attempts(user_id, notification_type, end_date.isoformat())
                if failed_attempts >= 3:
                    # Если было 3 или более неудачных попыток, записываем успешную отправку,
                    # чтобы прекратить попытки отправки этому пользователю
                    logging.info(f"Достигнут лимит попыток ({failed_attempts}) для пользователя {user_id}. Отмечаем уведомление как отправленное.")
                    add_notification_record(user_id, notification_type, end_date.isoformat(), 1)
            except BadRequest as e:
                logging.warning(f"Ошибка при отправке уведомления пользователю {user_id}: {e}")
                # Записываем неудачную попытку в историю
                add_notification_record(user_id, notification_type, end_date.isoformat(), 0)
                
                # Проверяем количество неудачных попыток
                failed_attempts = count_failed_notification_attempts(user_id, notification_type, end_date.isoformat())
                if failed_attempts >= 3:
                    # Если было 3 или более неудачных попыток, записываем успешную отправку,
                    # чтобы прекратить попытки отправки этому пользователю
                    logging.info(f"Достигнут лимит попыток ({failed_attempts}) для пользователя {user_id}. Отмечаем уведомление как отправленное.")
                    add_notification_record(user_id, notification_type, end_date.isoformat(), 1)
            except Exception as e:
                logging.error(f"Неожиданная ошибка при обработке уведомления для {user_id}: {e}")
                # Записываем неудачную попытку в историю
                add_notification_record(user_id, notification_type, end_date.isoformat(), 0)
                
                # Проверяем количество неудачных попыток
                failed_attempts = count_failed_notification_attempts(user_id, notification_type, end_date.isoformat())
                if failed_attempts >= 3:
                    # Если было 3 или более неудачных попыток, записываем успешную отправку,
                    # чтобы прекратить попытки отправки этому пользователю
                    logging.info(f"Достигнут лимит попыток ({failed_attempts}) для пользователя {user_id}. Отмечаем уведомление как отправленное.")
                    add_notification_record(user_id, notification_type, end_date.isoformat(), 1)

async def send_notification_message(bot, user_id, end_date, time_left, notification_type):
    """
    Формирует и отправляет сообщение с уведомлением пользователю
    
    Returns:
        bool: True если сообщение отправлено успешно, False если произошла ошибка
    """
    try:
        msk_tz = pytz.timezone('Europe/Moscow')
        now = datetime.now(msk_tz)
        
        # Убеждаемся, что end_date имеет timezone info
        if end_date.tzinfo is None:
            end_date = msk_tz.localize(end_date)
        else:
            end_date = end_date.astimezone(msk_tz)
        
        # Форматируем дату окончания в московском времени
        formatted_date = end_date.strftime('%d.%m.%Y %H:%M МСК')
        
        # Используем новую функцию для форматирования оставшегося времени
        time_left_str = format_time_left(end_date, now)
        
        # Формирование сообщения в зависимости от типа уведомления
        if notification_type == "3_дня":
            message = f"⚠️ *Подписка скоро закончится!*\n\n" \
                     f"Дата окончания: `{formatted_date}`\n" \
                     f"Осталось: `{time_left_str}`\n\n" \
                     f"Продли подписку в главном меню — Купить VPN"
                     
        elif notification_type == "24_часа":
            message = f"⚠️ *Подписка заканчивается!*\n\n" \
                     f"Дата окончания: `{formatted_date}`\n" \
                     f"Осталось: `{time_left_str}`\n\n" \
                     f"Не забудь продлить подписку!"
                     
        elif notification_type == "10_часов":
            message = f"⏰ *Срочно! Подписка скоро закончится!*\n\n" \
                     f"Дата окончания: `{formatted_date}`\n" \
                     f"Осталось всего: `{time_left_str}`\n\n" \
                     f"Пожалуйста, продли VPN сейчас, чтобы не потерять доступ."
                     
        else:  # "истекла"
            message = f"❌ *Подписка истекла!*\n\n" \
                     f"Дата окончания: `{formatted_date}`\n\n" \
                     f"Продли подписку в главном меню — Купить VPN"
        
        # Создание клавиатуры
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
        ])
        
        # Отправка сообщения
        await bot.send_message(
            user_id,
            message,
            parse_mode='Markdown',
            reply_markup=keyboard
        )
        
        logging.info(f"Отправлено уведомление '{notification_type}' пользователю {user_id}")
        return True
    except Exception as e:
        logging.error(f"Ошибка при отправке уведомления пользователю {user_id}: {e}")
        return False 