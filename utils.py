from datetime import datetime, timedelta
import pytz
import logging
from telegram import Bo<PERSON>
from telegram.error import TelegramError
import shutil
from config import ADMIN_IDS

def escape_markdown_v2(text):
    if text is None:
        return ''
    text = str(text)
    # Экранируем все зарезервированные символы MarkdownV2, включая точку
    reserved_chars = r'_*[]()~`>#+=|{}.!-'
    for char in reserved_chars:
        text = text.replace(char, f'\\{char}')
    return text

async def format_date(date_str):
    if not date_str:
        return 'Нет'
    try:
        date = datetime.fromisoformat(date_str)
        if date.tzinfo is None:
            date = pytz.UTC.localize(date)
        msk_tz = pytz.timezone('Europe/Moscow')
        return date.astimezone(msk_tz).strftime('%d.%m.%Y %H:%M МСК')
    except Exception:
        return 'Нет'

def format_time_left(end_date, current_time=None):
    """
    Форматирует оставшееся время до указанной даты в человекопонятном формате
    
    Args:
        end_date: Дата окончания (datetime с timezone)
        current_time: Текущее время (если None, используется текущее время в МСК)
        
    Returns:
        str: Отформатированная строка с оставшимся временем (например, "1 дн. 12 ч." или "3 ч. 45 мин.")
    """
    if not end_date:
        return "время не определено"
        
    # Используем текущее время МСК, если не указано иное
    if current_time is None:
        msk_tz = pytz.timezone('Europe/Moscow')
        current_time = datetime.now(msk_tz)
    
    # Убеждаемся, что даты имеют timezone info
    if end_date.tzinfo is None:
        msk_tz = pytz.timezone('Europe/Moscow')
        end_date = msk_tz.localize(end_date)
    
    # Вычисляем разницу
    time_diff = end_date - current_time
    
    # Если дата в прошлом
    if time_diff.total_seconds() <= 0:
        return "0 ч. (истекла)"
    
    days = time_diff.days
    seconds = time_diff.seconds
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    
    # Форматируем вывод в зависимости от оставшегося времени
    if days > 0:
        return f"{days} дн. {hours} ч."
    elif hours > 0:
        if hours < 10:  # Показываем минуты только если осталось меньше 10 часов
            return f"{hours} ч. {minutes} мин."
        else:
            return f"{hours} ч."
    else:
        return f"{minutes} мин."

async def check_channel_subscription(bot: Bot, user_id: int, channel_id: str) -> bool:
    """
    Проверяет, подписан ли пользователь на указанный канал

    Args:
        bot: Экземпляр бота Telegram
        user_id: ID пользователя Telegram
        channel_id: ID канала (например, "@spaacevpn")

    Returns:
        bool: True если пользователь подписан, False если нет
    """
    try:
        member = await bot.get_chat_member(chat_id=channel_id, user_id=user_id)
        # Проверяем статус участника канала
        # member, administrator, creator - подписан
        # left, kicked - не подписан
        return member.status in ['member', 'administrator', 'creator']
    except TelegramError as e:
        logging.error(f"Error checking channel subscription for user {user_id} in channel {channel_id}: {e}")
        # В случае ошибки (например, бот не админ канала) возвращаем False
        return False
    except Exception as e:
        logging.error(f"Unexpected error checking channel subscription: {e}")
        return False

async def send_db_backup(context):
    """
    Копирует базу данных и отправляет бэкап всем администраторам (через JobQueue).
    """
    bot = context.job.data["bot"]
    await _send_db_backup(bot)

async def send_db_backup_manual(bot):
    """
    Копирует базу данных и отправляет бэкап всем администраторам (ручной вызов).
    """
    await _send_db_backup(bot)

async def _send_db_backup(bot):
    try:
        msk_tz = pytz.timezone('Europe/Moscow')
        date_str = datetime.now(msk_tz).strftime('%Y-%m-%d')
        src_path = 'vpn_bot.db'
        backup_path = f'vpn_bot_backup_{date_str}.db'
        shutil.copy(src_path, backup_path)
        for admin_id in ADMIN_IDS:
            try:
                await bot.send_document(admin_id, open(backup_path, 'rb'), caption=f'Бэкап базы за {date_str}')
            except Exception as e:
                logging.error(f"Ошибка отправки бэкапа админу {admin_id}: {e}")
    except Exception as e:
        logging.error(f"Ошибка создания или отправки бэкапа: {e}")