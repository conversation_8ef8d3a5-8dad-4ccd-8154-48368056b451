import logging
import asyncio
import pytz
from datetime import datetime, timed<PERSON><PERSON>
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from database import get_db_connection
from remnawave_utils import user_exists, remnawave
from utils import format_date
from config import ADMIN_IDS

async def check_all_remnawave_users(context):
    msk_tz = pytz.timezone('Europe/Moscow')
    current_time = datetime.now(msk_tz)
    semaphore = asyncio.Semaphore(30)

    # Получаем всех пользователей Remnawave
    try:
        users_response = await remnawave.users.get_all_users_v2(start=0, size=1000)
        remnawave_users = users_response.users
    except Exception as e:
        logging.error(f"Failed to fetch users from Remnawave: {e}")
        # Уведомляем админов о сбое
        for admin_id in ADMIN_IDS:
            try:
                await context.bot.send_message(
                    admin_id,
                    f"❗️ Ошибка при получении пользователей Remnawave:\n{e}"
                )
            except Exception as err:
                logging.error(f"Failed to notify admin {admin_id} about Remnawave error: {err}")
        return

    # Получаем всех пользователей из вашей базы
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT user_id, marzban_username, expiry_notified FROM users')
        tg_users = c.fetchall()
        tg_map = {marzban_username: (user_id, expiry_notified) for user_id, marzban_username, expiry_notified in tg_users if marzban_username}

    async def process_user(rem_user):
        marzban_username = rem_user.username
        expire_at = rem_user.expire_at.astimezone(msk_tz) if rem_user.expire_at else None
        if marzban_username in tg_map:
            user_id, expiry_notified = tg_map[marzban_username]
            try:
                if expire_at:
                    time_left = (expire_at - current_time).total_seconds() / 3600
                    # Многоуровневая система уведомлений
                    # expiry_notified: 0 - не уведомлен, 1 - 3-дневное, 2 - 24-часовое, 3 - истекло
                    if 24 < time_left <= 72 and expiry_notified < 1:
                        await context.bot.send_message(
                            user_id,
                            f"⚠️ *Подписка скоро закончится!*\n\nДата окончания: `{expire_at.strftime('%d.%m.%Y %H:%M')}`\nОсталось: `{time_left:.1f}` часов\n\nПродли подписку в главном меню — Купить VPN",
                            parse_mode='Markdown',
                            reply_markup=InlineKeyboardMarkup([
                                [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
                                [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                            ])
                        )
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('UPDATE users SET expiry_notified = 1, subscription_end_date = ?, subscription_active = 1 WHERE user_id = ?', (expire_at.isoformat(), user_id))
                            conn.commit()
                        logging.info(f"Sent 3-day warning to user {user_id}")
                    elif 0 < time_left <= 24 and expiry_notified < 2:
                        await context.bot.send_message(
                            user_id,
                            f"⚠️ *Подписка заканчивается!*\n\nДата окончания: `{expire_at.strftime('%d.%m.%Y %H:%M')}`\nОсталось: `{time_left:.1f}` часов\n\nНе забудь продлить подписку!",
                            parse_mode='Markdown',
                            reply_markup=InlineKeyboardMarkup([
                                [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
                                [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                            ])
                        )
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('UPDATE users SET expiry_notified = 2, subscription_end_date = ?, subscription_active = 1 WHERE user_id = ?', (expire_at.isoformat(), user_id))
                            conn.commit()
                        logging.info(f"Sent 24-hour warning to user {user_id}")
                    elif time_left <= 0 and expiry_notified < 3:
                        await context.bot.send_message(
                            user_id,
                            f"❌ *Подписка истекла!*\n\nДата окончания: `{expire_at.strftime('%d.%m.%Y %H:%M')}`\n\nПродли подписку в главном меню — Купить VPN",
                            parse_mode='Markdown',
                            reply_markup=InlineKeyboardMarkup([
                                [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
                                [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                            ])
                        )
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('UPDATE users SET expiry_notified = 3, subscription_active = 0 WHERE user_id = ?', (user_id,))
                            conn.commit()
                        logging.info(f"Sent expiry notification to user {user_id}")
                    else:
                        # Просто обновляем дату окончания и статус
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('UPDATE users SET subscription_end_date = ?, subscription_active = 1 WHERE user_id = ?', (expire_at.isoformat(), user_id))
                            conn.commit()
                else:
                    # Нет даты окончания — подписка неактивна
                    with get_db_connection() as conn:
                        c = conn.cursor()
                        c.execute('UPDATE users SET subscription_active = 0 WHERE user_id = ?', (user_id,))
                        conn.commit()
            except Exception as user_err:
                logging.error(f"Error processing user {user_id}: {user_err}")
                # Уведомляем админов о сбое отправки уведомления
                for admin_id in ADMIN_IDS:
                    try:
                        await context.bot.send_message(
                            admin_id,
                            f"❗️ Ошибка при отправке уведомления пользователю {user_id} ({marzban_username}):\n{user_err}"
                        )
                    except Exception as err:
                        logging.error(f"Failed to notify admin {admin_id} about user error: {err}")

    await asyncio.gather(*(process_user(rem_user) for rem_user in remnawave_users))
    logging.info(f"Remnawave full user check completed for {len(remnawave_users)} users.")

# Alias for compatibility with existing imports
check_subscription_expiry = check_all_remnawave_users